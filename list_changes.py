#Python version - 3.8
#This script requires requests module installed in python.
from urllib.error import HTTPError
from urllib.parse import urlencode
from urllib.request import urlopen,Request

url = "https://servicedesk.grupoficohsa.hn:8443/api/v3/changes"
headers ={"Accept": "application/vnd.manageengine.sdp.v3+json", 
          "Authorization" : "authtoken:  26C90420-EF37-47C4-B5F0-1806AB64553A", 
          "Content-Type" : "application/x-www-form-urlencoded"}
input_data = '''{
    "list_info": {
        "row_count": 10,
        "start_index": 1,
        "get_total_count": true,
        "sort_fields": [
            {
                "field": "id",
                "order": "asc"
            }
        ]
    }
}'''       
url += "?" + urlencode({"input_data":input_data})
httprequest = Request(url, headers=headers)
try:
    with urlopen(httprequest) as response:
        print(response.read().decode())
except HTTPError as e:
    print(e.read().decode())