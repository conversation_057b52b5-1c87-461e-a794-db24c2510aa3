
import json
from urllib.error import HTTPError
from urllib.parse import urlencode
from urllib.request import urlopen, Request

url = "https://servicedesk.grupoficohsa.hn:8443/rest/api/v3/changes"
headers = {
    "Accept": "application/vnd.manageengine.sdp.v3+json",
    "Authorization": "Bearer 31A6FC56-06D6-4689-A364-66FAF1EDC8A3",  # Formato Bearer
    "Content-Type": "application/x-www-form-urlencoded"
}

input_data = {}

# Probar sin input_data primero
httprequest = Request(url, headers=headers, method='GET')

try:
    with urlopen(httprequest) as response:
        print(response.read().decode())
except HTTPError as e:
    print(e.read().decode())
